import React, { memo } from "react";
import type { LibraryGame, GameCollection } from "@types";

// Import existing modal components
import { CollectionModal } from "./collection-modal";
import { CollectionSelectorModal } from "./collection-selector-modal";
import { DeleteCollectionModal } from "./delete-collection-modal";

interface LibraryModalsProps {
  // Collection modal
  showCollectionModal: boolean;
  editingCollection: GameCollection | null;
  onCloseCollectionModal: () => void;
  onSaveCollection: (collection: Partial<GameCollection>) => void;

  // Collection selector modal
  showCollectionSelector: boolean;
  selectedGameForCollection: LibraryGame | null;
  collections: GameCollection[];
  onCloseCollectionSelector: () => void;
  onAddGameToCollection: (collectionId: string, game: LibraryGame) => void;

  // Delete collection modal
  showDeleteModal: boolean;
  collectionToDelete: GameCollection | null;
  onCloseDeleteModal: () => void;
  onConfirmDelete: (collectionId: string) => void;
}

export const LibraryModals = memo<LibraryModalsProps>(({
  // Collection modal props
  showCollectionModal,
  editingCollection,
  onCloseCollectionModal,
  onSaveCollection,

  // Collection selector modal props
  showCollectionSelector,
  selectedGameForCollection,
  collections,
  onCloseCollectionSelector,
  onAddGameToCollection,

  // Delete collection modal props
  showDeleteModal,
  collectionToDelete,
  onCloseDeleteModal,
  onConfirmDelete,
}) => {
  return (
    <>
      {/* Collection Creation/Edit Modal */}
      {showCollectionModal && (
        <CollectionModal
          visible={showCollectionModal}
          collection={editingCollection}
          onClose={onCloseCollectionModal}
          onSave={onSaveCollection}
        />
      )}

      {/* Collection Selector Modal */}
      {showCollectionSelector && selectedGameForCollection && (
        <CollectionSelectorModal
          visible={showCollectionSelector}
          game={selectedGameForCollection}
          collections={collections}
          onClose={onCloseCollectionSelector}
          onAddToCollection={onAddGameToCollection}
        />
      )}

      {/* Delete Collection Modal */}
      {showDeleteModal && collectionToDelete && (
        <DeleteCollectionModal
          visible={showDeleteModal}
          collection={collectionToDelete}
          onClose={onCloseDeleteModal}
          onConfirm={() => onConfirmDelete(collectionToDelete.id)}
        />
      )}
    </>
  );
});

LibraryModals.displayName = "LibraryModals";
