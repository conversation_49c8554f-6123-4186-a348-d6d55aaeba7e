@use "../../../scss/globals.scss";

.library-header {
  display: flex;
  flex-direction: column;
  gap: calc(globals.$spacing-unit * 2);
  padding: calc(globals.$spacing-unit * 3) 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 10;

  &__main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: calc(globals.$spacing-unit * 2);
  }

  &__left {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 2);
    flex: 1;
    min-width: 0;
  }

  &__right {
    display: flex;
    align-items: center;
    gap: globals.$spacing-unit;
  }

  &__collections-toggle {
    flex-shrink: 0;
  }

  &__search {
    flex: 1;
    max-width: 400px;
    min-width: 200px;
  }

  &__search-input {
    width: 100%;
  }

  &__filters,
  &__sort {
    position: relative;
  }

  &__filter-button {
    position: relative;
  }

  &__filter-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background: globals.$primary-color;
    border-radius: 50%;
    border: 2px solid globals.$background-color;
  }

  &__sort-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: calc(globals.$spacing-unit / 2);
    background: globals.$background-color;
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    z-index: 20;
    min-width: 160px;
    overflow: hidden;
  }

  &__sort-option {
    display: block;
    width: 100%;
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    background: none;
    border: none;
    color: globals.$body-color;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.05);
    }

    &--active {
      background: rgba(globals.$primary-color, 0.1);
      color: globals.$primary-color;
    }
  }

  &__stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: globals.$spacing-unit;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
  }

  &__game-count {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
  }

  &__filters-panel {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;
    padding: calc(globals.$spacing-unit * 2);
    margin-top: globals.$spacing-unit;
  }

  &__filters-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: calc(globals.$spacing-unit * 2);

    p {
      margin: 0;
      color: rgba(255, 255, 255, 0.7);
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    &__main {
      flex-direction: column;
      align-items: stretch;
      gap: calc(globals.$spacing-unit * 1.5);
    }

    &__left {
      order: 2;
    }

    &__right {
      order: 1;
      justify-content: space-between;
    }

    &__search {
      max-width: none;
    }
  }

  @media (max-width: 480px) {
    padding: calc(globals.$spacing-unit * 2) 0;

    &__right {
      gap: calc(globals.$spacing-unit / 2);
    }

    &__sort-dropdown {
      right: auto;
      left: 0;
    }
  }
}
