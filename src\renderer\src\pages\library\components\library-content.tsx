import React, { memo, useMemo, Suspense } from "react";
import { useTranslation } from "react-i18next";
import Skeleton, { SkeletonTheme } from "react-loading-skeleton";

import type { LibraryGame, LibraryViewMode, LibraryCardSize } from "@types";

// Lazy load heavy components for better performance
const LibraryGameGrid = React.lazy(() => 
  import("./library-game-grid").then(module => ({ default: module.LibraryGameGrid }))
);
const LibraryGameList = React.lazy(() => 
  import("./library-game-list").then(module => ({ default: module.LibraryGameList }))
);

import "./library-content.scss";

interface LibraryContentProps {
  games: LibraryGame[];
  viewMode: LibraryViewMode;
  cardSize: LibraryCardSize;
  isLoading?: boolean;
  searchQuery?: string;
  selectedCollection?: string | null;
  onAddToCollection?: (game: LibraryGame) => void;
  onRemoveFromCollection?: (game: LibraryGame) => void;
}

// Optimized skeleton component
const LibraryContentSkeleton = memo<{ viewMode: LibraryViewMode; count?: number }>(({ 
  viewMode, 
  count = 12 
}) => {
  const skeletonItems = useMemo(() => 
    Array.from({ length: count }, (_, index) => index), 
    [count]
  );

  if (viewMode === "grid") {
    return (
      <div className="library-content__skeleton library-content__skeleton--grid">
        {skeletonItems.map((index) => (
          <div key={index} className="library-content__skeleton-card">
            <Skeleton height={200} className="library-content__skeleton-image" />
            <div className="library-content__skeleton-content">
              <Skeleton height={20} className="library-content__skeleton-title" />
              <Skeleton height={16} width="60%" className="library-content__skeleton-subtitle" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="library-content__skeleton library-content__skeleton--list">
      {skeletonItems.map((index) => (
        <div key={index} className="library-content__skeleton-row">
          <Skeleton height={60} width={60} className="library-content__skeleton-icon" />
          <div className="library-content__skeleton-info">
            <Skeleton height={20} width="40%" className="library-content__skeleton-title" />
            <Skeleton height={16} width="20%" className="library-content__skeleton-subtitle" />
          </div>
          <Skeleton height={16} width="15%" className="library-content__skeleton-meta" />
          <Skeleton height={32} width={80} className="library-content__skeleton-actions" />
        </div>
      ))}
    </div>
  );
});

LibraryContentSkeleton.displayName = "LibraryContentSkeleton";

// Empty state component
const LibraryEmptyState = memo<{
  searchQuery?: string;
  selectedCollection?: string | null;
  onClearSearch?: () => void;
}>(({ searchQuery, selectedCollection, onClearSearch }) => {
  const { t } = useTranslation("library");

  const getEmptyStateContent = () => {
    if (searchQuery) {
      return {
        title: t("no_search_results"),
        description: t("no_search_results_description", { query: searchQuery }),
        action: onClearSearch ? (
          <button 
            type="button" 
            onClick={onClearSearch}
            className="library-content__empty-action"
          >
            {t("clear_search")}
          </button>
        ) : null,
      };
    }

    if (selectedCollection) {
      return {
        title: t("empty_collection"),
        description: t("empty_collection_description"),
        action: null,
      };
    }

    return {
      title: t("no_games"),
      description: t("no_games_description"),
      action: null,
    };
  };

  const { title, description, action } = getEmptyStateContent();

  return (
    <div className="library-content__empty">
      <div className="library-content__empty-content">
        <h3 className="library-content__empty-title">{title}</h3>
        <p className="library-content__empty-description">{description}</p>
        {action}
      </div>
    </div>
  );
});

LibraryEmptyState.displayName = "LibraryEmptyState";

// Main content component
export const LibraryContent = memo<LibraryContentProps>(({
  games,
  viewMode,
  cardSize,
  isLoading = false,
  searchQuery,
  selectedCollection,
  onAddToCollection,
  onRemoveFromCollection,
}) => {
  const { t } = useTranslation("library");

  // Show loading state
  if (isLoading) {
    return (
      <div className="library-content">
        <SkeletonTheme baseColor="#1c1c1c" highlightColor="#444">
          <LibraryContentSkeleton viewMode={viewMode} />
        </SkeletonTheme>
      </div>
    );
  }

  // Show empty state
  if (games.length === 0) {
    return (
      <div className="library-content">
        <LibraryEmptyState
          searchQuery={searchQuery}
          selectedCollection={selectedCollection}
          onClearSearch={() => {
            // This will be handled by parent component
          }}
        />
      </div>
    );
  }

  // Show games
  return (
    <div className="library-content">
      <Suspense 
        fallback={
          <SkeletonTheme baseColor="#1c1c1c" highlightColor="#444">
            <LibraryContentSkeleton viewMode={viewMode} />
          </SkeletonTheme>
        }
      >
        {viewMode === "grid" ? (
          <LibraryGameGrid
            games={games}
            cardSize={cardSize}
            onAddToCollection={onAddToCollection}
            onRemoveFromCollection={onRemoveFromCollection}
          />
        ) : (
          <LibraryGameList
            games={games}
            onAddToCollection={onAddToCollection}
            onRemoveFromCollection={onRemoveFromCollection}
          />
        )}
      </Suspense>
    </div>
  );
});

LibraryContent.displayName = "LibraryContent";
