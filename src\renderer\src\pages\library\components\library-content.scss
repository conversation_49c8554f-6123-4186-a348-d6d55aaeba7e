@use "../../../scss/globals.scss";

.library-content {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  overflow-x: hidden;

  // Smooth scrolling
  scroll-behavior: smooth;
  
  // Custom scrollbar
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    
    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }

  // Skeleton states
  &__skeleton {
    padding: calc(globals.$spacing-unit * 2);
    animation: fadeIn 0.3s ease-out;

    &--grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: calc(globals.$spacing-unit * 2);

      @media (min-width: 1400px) {
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      }

      @media (max-width: 768px) {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: globals.$spacing-unit;
        padding: globals.$spacing-unit;
      }
    }

    &--list {
      display: flex;
      flex-direction: column;
      gap: calc(globals.$spacing-unit * 1.5);
    }
  }

  &__skeleton-card {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    overflow: hidden;
    padding: calc(globals.$spacing-unit * 1.5);
  }

  &__skeleton-image {
    border-radius: 8px;
    margin-bottom: calc(globals.$spacing-unit * 1.5);
  }

  &__skeleton-content {
    display: flex;
    flex-direction: column;
    gap: globals.$spacing-unit;
  }

  &__skeleton-row {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 2);
    padding: calc(globals.$spacing-unit * 1.5);
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;
  }

  &__skeleton-icon {
    border-radius: 6px;
    flex-shrink: 0;
  }

  &__skeleton-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: calc(globals.$spacing-unit / 2);
  }

  &__skeleton-meta,
  &__skeleton-actions {
    flex-shrink: 0;
  }

  // Empty state
  &__empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    padding: calc(globals.$spacing-unit * 4);
  }

  &__empty-content {
    text-align: center;
    max-width: 400px;
  }

  &__empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: globals.$body-color;
    margin: 0 0 calc(globals.$spacing-unit * 1.5) 0;
  }

  &__empty-description {
    color: rgba(255, 255, 255, 0.7);
    margin: 0 0 calc(globals.$spacing-unit * 2) 0;
    line-height: 1.5;
  }

  &__empty-action {
    background: globals.$primary-color;
    color: white;
    border: none;
    border-radius: 6px;
    padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: lighten(globals.$primary-color, 10%);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
    }
  }

  // Animations
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // Performance optimizations
  .react-loading-skeleton {
    --base-color: #1c1c1c;
    --highlight-color: #444;
    --animation-duration: 1.5s;
    --animation-direction: normal;
    --pseudo-element-display: block;
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    &__empty {
      min-height: 300px;
      padding: calc(globals.$spacing-unit * 3);
    }

    &__empty-title {
      font-size: 1.25rem;
    }
  }

  @media (max-width: 480px) {
    &__skeleton {
      padding: globals.$spacing-unit;
    }

    &__empty {
      padding: calc(globals.$spacing-unit * 2);
    }
  }
}
