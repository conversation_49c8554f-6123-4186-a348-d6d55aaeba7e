import { useMemo } from "react";
import type { LibraryGame, LibraryFilters, LibrarySortBy, GameCollection } from "@types";
import { filterGamesByGenres } from "@renderer/utils/genre-utils";
import { sortLibraryGames } from "@renderer/utils/library-sort-utils";
import { wasPlayedRecently } from "@renderer/utils/date-utils";

interface UseLibraryGamesProps {
  library: LibraryGame[];
  filters: LibraryFilters;
  selectedCollection: string | null;
  collections: GameCollection[];
}

interface UseLibraryGamesReturn {
  filteredAndSortedGames: LibraryGame[];
  totalGames: number;
  filteredGames: number;
}

/**
 * Optimized hook for filtering and sorting library games
 * Uses memoization to prevent unnecessary recalculations
 */
export function useLibraryGames({
  library,
  filters,
  selectedCollection,
  collections,
}: UseLibraryGamesProps): UseLibraryGamesReturn {
  const filteredAndSortedGames = useMemo(() => {
    let games = [...library];

    // Apply search filter first (most common filter)
    if (filters.searchQuery?.trim()) {
      const searchTerm = filters.searchQuery.toLowerCase().trim();
      games = games.filter(game =>
        game.title.toLowerCase().includes(searchTerm) ||
        game.shop?.toLowerCase().includes(searchTerm)
      );
    }

    // Apply collection filter
    if (selectedCollection) {
      const collection = collections.find((c) => c.id === selectedCollection);
      if (collection) {
        // Regular collection
        games = games.filter((game) => collection.gameIds.includes(game.id));
      } else {
        // Smart collection
        switch (selectedCollection) {
          case "recently-played":
            games = games.filter(game => wasPlayedRecently(game.lastTimePlayed));
            break;
          case "favorites":
            games = games.filter(game => game.favorite);
            break;
          case "installed":
            games = games.filter(game => Boolean(game.executablePath));
            break;
          case "not-played":
            games = games.filter(game => !game.playTimeInMilliseconds || game.playTimeInMilliseconds === 0);
            break;
          default:
            // Unknown collection, show all games
            break;
        }
      }
    }

    // Apply genre filters
    if (filters.genres && filters.genres.length > 0) {
      games = filterGamesByGenres(games, filters.genres);
    }

    // Apply other filters
    if (filters.installed !== undefined) {
      games = games.filter(game => Boolean(game.executablePath) === filters.installed);
    }

    if (filters.favorite !== undefined) {
      games = games.filter(game => Boolean(game.favorite) === filters.favorite);
    }

    if (filters.hasPlaytime !== undefined) {
      games = games.filter(game => {
        const hasPlaytime = Boolean(game.playTimeInMilliseconds && game.playTimeInMilliseconds > 0);
        return hasPlaytime === filters.hasPlaytime;
      });
    }

    // Apply sorting
    games = sortLibraryGames(games, filters.sortBy || "name");

    return games;
  }, [library, filters, selectedCollection, collections]);

  return {
    filteredAndSortedGames,
    totalGames: library.length,
    filteredGames: filteredAndSortedGames.length,
  };
}

/**
 * Hook for getting smart collection counts
 * Memoized for performance
 */
export function useSmartCollectionCounts(library: LibraryGame[]) {
  return useMemo(() => ({
    "recently-played": library.filter(game => wasPlayedRecently(game.lastTimePlayed)).length,
    "favorites": library.filter(game => game.favorite).length,
    "installed": library.filter(game => Boolean(game.executablePath)).length,
    "not-played": library.filter(game => !game.playTimeInMilliseconds || game.playTimeInMilliseconds === 0).length,
  }), [library]);
}

/**
 * Hook for getting collection statistics
 */
export function useCollectionStats(
  library: LibraryGame[],
  collections: GameCollection[]
) {
  return useMemo(() => {
    const stats = new Map<string, number>();
    
    collections.forEach(collection => {
      const gameCount = collection.gameIds.filter(gameId =>
        library.some(game => game.id === gameId)
      ).length;
      stats.set(collection.id, gameCount);
    });

    return stats;
  }, [library, collections]);
}
