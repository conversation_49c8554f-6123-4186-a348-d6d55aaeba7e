@use "../../scss/globals.scss";

.library {
  width: 100%;
  height: 100%;
  display: flex;
  background: globals.$background-color;
  position: relative;
  overflow: hidden;

  &__main {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    padding: calc(globals.$spacing-unit * 3);
    gap: calc(globals.$spacing-unit * 3);
    overflow: hidden;

    // Smooth transitions for content changes
    transition: all 0.3s ease;

    // When sidebar is visible, adjust padding
    .library-collections-sidebar.library-collections-sidebar--visible ~ & {
      padding-left: calc(globals.$spacing-unit * 2);
    }
  }

  // Responsive design
  @media (max-width: 1280px) {
    &__main {
      padding: calc(globals.$spacing-unit * 2.5);
      gap: calc(globals.$spacing-unit * 2.5);
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;

    &__main {
      padding: calc(globals.$spacing-unit * 2);
      gap: calc(globals.$spacing-unit * 2);
    }
  }

  @media (max-width: 480px) {
    &__main {
      padding: calc(globals.$spacing-unit * 1.5);
      gap: calc(globals.$spacing-unit * 1.5);
    }
  }

  // Performance optimizations
  * {
    will-change: auto;
  }

  // Smooth transitions for all interactive elements
  button, .clickable {
    transition: all 0.2s ease;
  }

  // Loading states
  &--loading {
    .library__main {
      opacity: 0.7;
      pointer-events: none;
    }
  }

}
