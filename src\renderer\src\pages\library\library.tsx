import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useSearchParams } from "react-router-dom";

import { useLibrary, useLibraryCollections } from "@renderer/hooks";
import type { LibraryGame, GameCollection } from "@types";

// Core components
import { LibraryHeader } from "./components/library-header";
import { LibraryContent } from "./components/library-content";
import { LibraryModals } from "./components/library-modals";
import { LibraryCollectionsSidebar } from "./components/library-collections-sidebar";
import { LibraryNews } from "./components/library-news";

// Hooks
import { useLibraryGames } from "./hooks/use-library-games";

import "./library.scss";

export default function Library() {
  const { t } = useTranslation("library");
  const { library, isLoading: libraryLoading } = useLibrary();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const {
    viewMode,
    cardSize,
    filters,
    selectedCollection,
    collections,
    changeViewMode,
    changeCardSize,
    updateFilters,
    updateSortBy,
    resetFilters,
    selectCollection,
    loadCollections,
    removeGameFromCollectionById,
    addGameToCollectionById,
    createCollection,
    editCollection,
    deleteCollection,
  } = useLibraryCollections();

  // Get filtered and sorted games using optimized hook
  const { filteredAndSortedGames, totalGames, filteredGames } = useLibraryGames({
    library,
    filters,
    selectedCollection,
    collections,
  });

  // Local state
  const [showCollectionsSidebar, setShowCollectionsSidebar] = useState(false);
  const [showCollectionModal, setShowCollectionModal] = useState(false);
  const [showCollectionSelector, setShowCollectionSelector] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedGameForCollection, setSelectedGameForCollection] = useState<LibraryGame | null>(null);
  const [editingCollection, setEditingCollection] = useState<GameCollection | null>(null);
  const [collectionToDelete, setCollectionToDelete] = useState<GameCollection | null>(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Handle URL parameters for collection navigation
  useEffect(() => {
    const collectionParam = searchParams.get("collection");
    if (collectionParam !== selectedCollection) {
      selectCollection(collectionParam, "navigation");
    }
  }, [searchParams, selectedCollection, selectCollection]);

  // Load collections on mount
  useEffect(() => {
    loadCollections();
  }, [loadCollections]);

  // Event handlers with smooth transitions
  const handleSearchChange = useCallback((value: string) => {
    setIsTransitioning(true);
    updateFilters({ searchQuery: value });
    setTimeout(() => setIsTransitioning(false), 150);
  }, [updateFilters]);

  const handleSortChange = useCallback((sortBy: any) => {
    setIsTransitioning(true);
    updateSortBy(sortBy);
    setTimeout(() => setIsTransitioning(false), 150);
  }, [updateSortBy]);

  const handleViewModeChange = useCallback((mode: any) => {
    changeViewMode(mode);
  }, [changeViewMode]);

  const handleCollectionSelect = useCallback((collectionId: string | null) => {
    setIsTransitioning(true);
    selectCollection(collectionId, "navigation");
    setTimeout(() => setIsTransitioning(false), 200);
  }, [selectCollection]);

  const handleToggleCollectionsSidebar = useCallback(() => {
    setShowCollectionsSidebar(prev => !prev);
  }, []);

  const handleClearFilters = useCallback(() => {
    setIsTransitioning(true);
    resetFilters();
    setTimeout(() => setIsTransitioning(false), 150);
  }, [resetFilters]);

  // Collection management handlers
  const handleCreateCollection = useCallback(() => {
    setEditingCollection(null);
    setShowCollectionModal(true);
  }, []);

  const handleEditCollection = useCallback((collection: GameCollection) => {
    setEditingCollection(collection);
    setShowCollectionModal(true);
  }, []);

  const handleDeleteCollection = useCallback((collection: GameCollection) => {
    setCollectionToDelete(collection);
    setShowDeleteModal(true);
  }, []);

  const handleSaveCollection = useCallback(async (collectionData: Partial<GameCollection>) => {
    try {
      if (editingCollection) {
        await editCollection(editingCollection.id, collectionData);
      } else {
        await createCollection(collectionData);
      }
      setShowCollectionModal(false);
      setEditingCollection(null);
    } catch (error) {
      console.error("Failed to save collection:", error);
    }
  }, [editingCollection, editCollection, createCollection]);

  const handleConfirmDelete = useCallback(async (collectionId: string) => {
    try {
      await deleteCollection(collectionId);
      setShowDeleteModal(false);
      setCollectionToDelete(null);
    } catch (error) {
      console.error("Failed to delete collection:", error);
    }
  }, [deleteCollection]);

  // Game collection handlers
  const handleAddGameToCollection = useCallback((game: LibraryGame) => {
    setSelectedGameForCollection(game);
    setShowCollectionSelector(true);
  }, []);

  const handleRemoveGameFromCollection = useCallback(async (game: LibraryGame) => {
    if (!selectedCollection) return;
    try {
      await removeGameFromCollectionById(selectedCollection, game.id);
    } catch (error) {
      console.error("Failed to remove game from collection:", error);
    }
  }, [selectedCollection, removeGameFromCollectionById]);

  const handleAddToCollection = useCallback(async (collectionId: string, game: LibraryGame) => {
    try {
      await addGameToCollectionById(collectionId, game.id);
      setShowCollectionSelector(false);
      setSelectedGameForCollection(null);
    } catch (error) {
      console.error("Failed to add game to collection:", error);
    }
  }, [addGameToCollectionById]);

  // Modal close handlers
  const handleCloseCollectionModal = useCallback(() => {
    setShowCollectionModal(false);
    setEditingCollection(null);
  }, []);

  const handleCloseCollectionSelector = useCallback(() => {
    setShowCollectionSelector(false);
    setSelectedGameForCollection(null);
  }, []);

  const handleCloseDeleteModal = useCallback(() => {
    setShowDeleteModal(false);
    setCollectionToDelete(null);
  }, []);

  const isLoading = libraryLoading || isTransitioning;

  return (
    <div className="library">
      {/* Collections Sidebar */}
      <LibraryCollectionsSidebar
        collections={collections}
        selectedCollection={selectedCollection}
        library={library}
        isVisible={showCollectionsSidebar}
        onSelectCollection={handleCollectionSelect}
        onCreateCollection={handleCreateCollection}
        onEditCollection={handleEditCollection}
        onDeleteCollection={handleDeleteCollection}
        onClose={() => setShowCollectionsSidebar(false)}
      />

      {/* Main Content */}
      <div className="library__main">
        {/* Header with search, filters, and controls */}
        <LibraryHeader
          games={filteredAndSortedGames}
          searchQuery={filters.searchQuery || ""}
          sortBy={filters.sortBy || "name"}
          viewMode={viewMode}
          cardSize={cardSize}
          filters={filters}
          onSearchChange={handleSearchChange}
          onSortChange={handleSortChange}
          onViewModeChange={handleViewModeChange}
          onCardSizeChange={changeCardSize}
          onToggleCollectionsSidebar={handleToggleCollectionsSidebar}
          onUpdateFilters={updateFilters}
          onClearFilters={handleClearFilters}
          collectionsVisible={showCollectionsSidebar}
          isLoading={isLoading}
        />

        {/* News Section */}
        {library.length > 0 && !selectedCollection && <LibraryNews />}

        {/* Games Content */}
        <LibraryContent
          games={filteredAndSortedGames}
          viewMode={viewMode}
          cardSize={cardSize}
          isLoading={isLoading}
          searchQuery={filters.searchQuery}
          selectedCollection={selectedCollection}
          onAddToCollection={handleAddGameToCollection}
          onRemoveFromCollection={handleRemoveGameFromCollection}
        />
      </div>

      {/* Modals */}
      <LibraryModals
        showCollectionModal={showCollectionModal}
        editingCollection={editingCollection}
        onCloseCollectionModal={handleCloseCollectionModal}
        onSaveCollection={handleSaveCollection}
        showCollectionSelector={showCollectionSelector}
        selectedGameForCollection={selectedGameForCollection}
        collections={collections}
        onCloseCollectionSelector={handleCloseCollectionSelector}
        onAddGameToCollection={handleAddToCollection}
        showDeleteModal={showDeleteModal}
        collectionToDelete={collectionToDelete}
        onCloseDeleteModal={handleCloseDeleteModal}
        onConfirmDelete={handleConfirmDelete}
      />
    </div>
  );
}
