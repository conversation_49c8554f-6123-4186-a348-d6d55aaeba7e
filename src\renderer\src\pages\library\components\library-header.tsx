import React, { memo, useCallback, useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import {
  SearchIcon,
  FilterIcon,
  SortAscIcon,
  SortDescIcon,
  SquareIcon,
  SquareFillIcon,
  ListUnorderedIcon,
  SidebarExpandIcon,
  SidebarCollapseIcon,
  GearIcon,
} from "@primer/octicons-react";

import { Button, TextField } from "@renderer/components";
import type { LibraryGame, LibraryFilters, LibrarySortBy, LibraryViewMode, LibraryCardSize } from "@types";

import "./library-header.scss";

interface LibraryHeaderProps {
  games: LibraryGame[];
  searchQuery: string;
  sortBy: LibrarySortBy;
  viewMode: LibraryViewMode;
  cardSize: LibraryCardSize;
  filters: LibraryFilters;
  onSearchChange: (value: string) => void;
  onSortChange: (sortBy: LibrarySortBy) => void;
  onViewModeChange: (mode: LibraryViewMode) => void;
  onCardSizeChange: (size: LibraryCardSize) => void;
  onToggleCollectionsSidebar: () => void;
  onUpdateFilters: (filters: Partial<LibraryFilters>) => void;
  onClearFilters: () => void;
  collectionsVisible: boolean;
  isLoading?: boolean;
}

export const LibraryHeader = memo<LibraryHeaderProps>(({
  games,
  searchQuery,
  sortBy,
  viewMode,
  cardSize,
  filters,
  onSearchChange,
  onSortChange,
  onViewModeChange,
  onCardSizeChange,
  onToggleCollectionsSidebar,
  onUpdateFilters,
  onClearFilters,
  collectionsVisible,
  isLoading = false,
}) => {
  const { t } = useTranslation("library");
  const [showFilters, setShowFilters] = useState(false);
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  const sortDropdownRef = useRef<HTMLDivElement>(null);

  const handleSearchChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      onSearchChange(event.target.value);
    },
    [onSearchChange]
  );

  const handleSortChange = useCallback(
    (newSortBy: LibrarySortBy) => {
      onSortChange(newSortBy);
      setShowSortDropdown(false);
    },
    [onSortChange]
  );

  const handleViewModeToggle = useCallback(() => {
    onViewModeChange(viewMode === "grid" ? "list" : "grid");
  }, [viewMode, onViewModeChange]);

  const handleCardSizeChange = useCallback(() => {
    const sizes: LibraryCardSize[] = ["compact", "normal", "large"];
    const currentIndex = sizes.indexOf(cardSize);
    const nextIndex = (currentIndex + 1) % sizes.length;
    onCardSizeChange(sizes[nextIndex]);
  }, [cardSize, onCardSizeChange]);

  const sortOptions = [
    { value: "name", label: t("sort_by_name") },
    { value: "recent", label: t("sort_by_recent") },
    { value: "playtime", label: t("sort_by_playtime") },
    { value: "size", label: t("sort_by_size") },
  ] as const;

  const hasActiveFilters = Object.values(filters).some(value => 
    Array.isArray(value) ? value.length > 0 : Boolean(value)
  );

  return (
    <header className="library-header">
      <div className="library-header__main">
        {/* Left section - Collections toggle and search */}
        <div className="library-header__left">
          <Button
            onClick={onToggleCollectionsSidebar}
            theme="outline"
            size="small"
            className="library-header__collections-toggle"
            title={collectionsVisible ? t("hide_collections") : t("show_collections")}
          >
            {collectionsVisible ? <SidebarCollapseIcon size={16} /> : <SidebarExpandIcon size={16} />}
          </Button>

          <div className="library-header__search">
            <TextField
              value={searchQuery}
              onChange={handleSearchChange}
              placeholder={t("search_games")}
              leftIcon={<SearchIcon size={16} />}
              className="library-header__search-input"
              disabled={isLoading}
            />
          </div>
        </div>

        {/* Right section - Controls */}
        <div className="library-header__right">
          {/* Filters */}
          <div className="library-header__filters" ref={sortDropdownRef}>
            <Button
              onClick={() => setShowFilters(!showFilters)}
              theme={hasActiveFilters ? "primary" : "outline"}
              size="small"
              className="library-header__filter-button"
              title={t("filters")}
            >
              <FilterIcon size={16} />
              {hasActiveFilters && <span className="library-header__filter-badge" />}
            </Button>
          </div>

          {/* Sort */}
          <div className="library-header__sort" ref={sortDropdownRef}>
            <Button
              onClick={() => setShowSortDropdown(!showSortDropdown)}
              theme="outline"
              size="small"
              className="library-header__sort-button"
              title={t("sort")}
            >
              {sortBy === "name" ? <SortAscIcon size={16} /> : <SortDescIcon size={16} />}
            </Button>

            {showSortDropdown && (
              <div className="library-header__sort-dropdown">
                {sortOptions.map((option) => (
                  <button
                    key={option.value}
                    type="button"
                    className={`library-header__sort-option ${
                      sortBy === option.value ? "library-header__sort-option--active" : ""
                    }`}
                    onClick={() => handleSortChange(option.value)}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* View mode toggle */}
          <Button
            onClick={handleViewModeToggle}
            theme="outline"
            size="small"
            className="library-header__view-toggle"
            title={viewMode === "grid" ? t("switch_to_list") : t("switch_to_grid")}
          >
            {viewMode === "grid" ? <ListUnorderedIcon size={16} /> : <SquareFillIcon size={16} />}
          </Button>

          {/* Card size (only for grid view) */}
          {viewMode === "grid" && (
            <Button
              onClick={handleCardSizeChange}
              theme="outline"
              size="small"
              className="library-header__card-size"
              title={t("card_size")}
            >
              <SquareIcon size={cardSize === "compact" ? 12 : cardSize === "normal" ? 16 : 20} />
            </Button>
          )}
        </div>
      </div>

      {/* Game count */}
      <div className="library-header__stats">
        <span className="library-header__game-count">
          {t("games_count", { count: games.length })}
        </span>
      </div>

      {/* Filters panel */}
      {showFilters && (
        <div className="library-header__filters-panel">
          {/* TODO: Add filter controls here */}
          <div className="library-header__filters-content">
            <p>{t("filters_coming_soon")}</p>
            <Button
              onClick={onClearFilters}
              theme="outline"
              size="small"
              disabled={!hasActiveFilters}
            >
              {t("clear_filters")}
            </Button>
          </div>
        </div>
      )}
    </header>
  );
});

LibraryHeader.displayName = "LibraryHeader";
